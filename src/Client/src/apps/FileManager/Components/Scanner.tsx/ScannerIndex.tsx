import { FC,  useState } from "react";
import { <PERSON><PERSON>, Card, message } from "antd";
import { ScanOutlined, DeleteOutlined } from "@ant-design/icons";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import { useTranslation } from "react-i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { bulkUploadFile, getFilesListFilter } from "../../Services";

const ScannerIndex: FC<{ folderPath: string; onFinishSelectFile: any }> = ({
  folderPath,
  onFinishSelectFile
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation();
  const [scannedFiles, setScannedFiles] = useState<string[]>([
    
  ]);
  const [activeIndex, setActiveIndex] = useState(0);

  const handleScan = () => {
    if (!window.scanner) {
      message.error("Tarayıcı bağlı değil!");
      return;
    }

    window.scanner.scan(
      (successful: boolean, mesg: string, response: string) => {
        if (!successful) {
          message.error(`Tarama başarısız: ${mesg}`);
          return;
        }

        if (mesg?.toLowerCase().includes("user cancel")) {
          message.info("Tarama iptal edildi.");
          return;
        }

        try {
          const scannedImages = window.scanner.getScannedImages(
            response,
            true,
            false
          );
          if (Array.isArray(scannedImages) && scannedImages.length > 0) {
            const newImages = scannedImages.map((img: any) => img.src);
            setScannedFiles((prev) => [...prev, ...newImages]);
            setActiveIndex(scannedFiles.length); // yeni eklenen ilk resim aktif olur
          } else {
            message.warning("Herhangi bir görüntü alınamadı.");
          }
        } catch (err) {
          console.error("Tarama sonrası hata:", err);
          message.error("Tarama sonrası işleme hatası oluştu.");
        }
      },
      {
        output_settings: [
          {
            type: "return-base64",
            format: "jpg",
          },
        ],
      }
    );
  };

  const handleDelete = (index: number) => {
    const updated = scannedFiles.filter((_, i) => i !== index);
    setScannedFiles(updated);
    setActiveIndex((prev) =>
      index === prev ? 0 : index < prev ? prev - 1 : prev
    );
  };

  const handleUploadScannedFile = async () => {
    if (scannedFiles.length === 0) {
      message.warning("Yüklenecek taranmış dosya bulunamadı.");
      return;
    }
  
    setIsLoading(true);
    const formData = new FormData();
    const finalFolderPath = folderPath || "uploads";
    formData.append("FolderPath", finalFolderPath);
  
    scannedFiles.forEach((base64DataUrl, index) => {
      // Base64'ü Blob'a çevir
      const byteString = atob(base64DataUrl.split(",")[1]);
      const mimeString = base64DataUrl.split(",")[0].split(":")[1].split(";")[0];
      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      const blob = new Blob([ab], { type: mimeString });
  
      const fileName = `scan-${Date.now()}-${index + 1}.jpg`;
      const file = new File([blob], fileName, { type: mimeString });
  
      formData.append("Files", file);
    });
  
    try {
      const res = await bulkUploadFile(formData);
  
      if (res?.Value?.length > 0) {
        openNotificationWithIcon(
          "success",
          t("ticket.list.filesUploadedSuccessfully")
        );
        const files = await getFilesListFilter({
          PageSize: 100,
          fileIds: res?.Value,
        });
        onFinishSelectFile(files?.Value);
      }
    } catch (err) {
      showErrorCatching(err, null, false, t);
    } finally {
      setIsLoading(false);
    }
  };



  
  

  return (
    <div className="bg-gray-100 p-4 !mt-8">

      <div className="max-w-3xl mx-auto !flex flex-col gap-6">
        <Card className="shadow-md rounded-xl">
          <h1 className="text-2xl font-bold mb-4 text-center">
           {t("fileManager.scanFile")}
          </h1>
          <p className="text-gray-600 text-center mb-4">
            {t("fileManager.scanFileDesc")}
          </p>
          <div className="flex justify-center">
            <Button
              type="primary"
              icon={<ScanOutlined />}
              size="large"
              onClick={handleScan}
            >
              {t("fileManager.scan")}
            </Button>
          </div>
        </Card>

        <div className="relative bg-white rounded-xl shadow p-4 h-[600px] flex items-center justify-center ">
          {/* Köşeler */}
          <div className="absolute top-0 left-0 w-10 h-10 border-l-4 border-t-4 border-[#0096d1] rounded-tl-lg" />
          <div className="absolute top-0 right-0 w-10 h-10 border-r-4 border-t-4 border-[#0096d1] rounded-tr-lg" />
          <div className="absolute bottom-0 left-0 w-10 h-10 border-l-4 border-b-4 border-[#0096d1] rounded-bl-lg" />
          <div className="absolute bottom-0 right-0 w-10 h-10 border-r-4 border-b-4 border-[#0096d1] rounded-br-lg" />

          {scannedFiles.length > 0 ? (
            <img
              src={scannedFiles[activeIndex]}
              alt="Scanned Preview"
              className="max-w-full max-h-[500px] object-contain"
            />
          ) : (
            <p className="text-gray-500">Henüz taranmış bir belge yok.</p>
          )}
        </div>

        <div className="flex gap-2 overflow-x-auto px-1 max-w-full scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
          {scannedFiles.map((src, i) => (
            <div
              key={i}
              className="relative group w-[120px] flex-shrink-0 cursor-pointer"
            >
              <img
                onClick={() => setActiveIndex(i)}
                src={src}
                className={`h-20 w-full object-cover border-2 rounded-md ${
                  i === activeIndex ? "border-blue-500" : "border-gray-300"
                }`}
              />
              <DeleteOutlined
                onClick={() => handleDelete(i)}
                className="absolute top-1 right-1 text-white bg-red-500 rounded-full p-1 text-xs z-10 opacity-0 group-hover:opacity-100 transition-opacity"
              />
            </div>
          ))}
        </div>

        <div className="!flex gap-2">
          <MazakaButton
          loading={isLoading}
          disabled={isLoading}
          status="save" onClick={handleUploadScannedFile}>
            {t("fileManager.save")}
          </MazakaButton>
        </div>
      </div>

   
    </div>
  );
};

export default ScannerIndex;
