import useMazakaForm from "@/hooks/useMazakaForm";
import { RootState } from "@/store/Reducers";
import { Col, Form, Row } from "antd";
import { useTranslation } from "react-i18next";
import { useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { createTicketComment, updateTicketWithPut } from "../../Services";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import endPoints from "../../EndPoints";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaButton } from "@/apps/Common/MazakaButton";
import ReusableFileManager, { FileItem } from "@/apps/Common/ReusableFileManager";
import React, { useState, useCallback, useEffect } from "react";
import SlateEditor from "@/apps/Common/Editor/SlateEditor";
import { hanldleSetTicketDetails } from "../../ClientSideStates";
import { Descendant } from "slate";



const AddOrUpdateComment = () => {
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const { ticketDetails} = useSelector((state: RootState) => state.ticket);
  const [mentionedUserIds, setMentionedUserIds] = useState<string[]>([]);
  const dispatch = useDispatch();

 const [editorValue, setEditorValue] = useState<Descendant[]>(
      [
        {
          type: "paragraph",
          children: [{ text: "" }],
        },
      ]
    );

  // File management state
  const [fileList, setFileList] = useState<FileItem[]>([]);

  // Debug: File list değişikliklerini logla
  React.useEffect(() => {
    fileList.forEach((file, index) => {
    
    });
  }, [fileList]);


  const handleMentionChange = (mentions: Array<{Name: string, Surname: string, Id: string}>) => {
 
  
  // Mention'lardan sadece ID'leri al
  const userIds = mentions.map(mention => mention.Id);
  
  // State'i güncelle
  setMentionedUserIds(userIds);

};

useEffect(() => {

}, [mentionedUserIds]);



 const handleOnFinish =async()=> {
  mazakaForm.setLoading();
  const formValues = form.getFieldsValue();
  formValues["TicketId"] = ticketDetails?.Id;
 console.log(formValues)

  // SlateEditor'dan gelen Comment verisini JSON string'e çevir
  if (editorValue?.length>0) {

    formValues["Comment"] = JSON.stringify(editorValue);
    
  }


  // Dosyaları forma ekle
  if (fileList.length > 0) {
    formValues["Files"] = fileList;
  }

  if (mentionedUserIds.length > 0) {
  const existing = ticketDetails?.Watchlist || [];
  const newIds = mentionedUserIds.filter(id => !existing.includes(id));
  const updatedDetails = {
    ...ticketDetails,
    Watchlist: [...existing, ...newIds],
  };
 
  await updateTicketWithPut(updatedDetails);
  dispatch(hanldleSetTicketDetails({ data: updatedDetails }));
}


  try {
     

    await createTicketComment(formValues);

    mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
    openNotificationWithIcon("success", t("form.transactionSuccessful"));

    queryClient.resetQueries({
      queryKey: endPoints.getTicketComments,
      exact: false,
    });
    queryClient.resetQueries({
      queryKey: endPoints.getTicketDetail,
      exact: false,
    });

    // Form ve dosyaları temizle
    form.resetFields();
    setFileList([]);
    setEditorValue([])
  } catch (error) {
    showErrorCatching(error, mazakaForm, true, t);
  }
};

// Verileri gösterirken parse etmek için yardımcı fonksiyon
const parseCommentData = (commentString: string) => {
  try {
    return JSON.parse(commentString);
  } catch (error) {
    console.error('Comment parse edilemedi:', error);
    return commentString; // Parse edilemezse string olarak döndür
  }
};

// Eğer mevcut yorumları göstermek için kullanacaksanız:
const displayComment = (commentData: string | object) => {
  if (typeof commentData === 'string') {
    return parseCommentData(commentData);
  }
  return commentData;
};

// Form'a veri set ederken (düzenleme durumunda)
const setFormData = (commentData: any) => {
  const parsedData = typeof commentData.Comment === 'string' 
    ? parseCommentData(commentData.Comment) 
    : commentData.Comment;
    
  form.setFieldsValue({
    Comment: parsedData,
  });
};



  return (
    <>
      <MazakaForm
        form={form}
        onFinish={handleOnFinish}
        submitButtonVisible={false}
      >
        <Row gutter={[0, 20]}>
          {/* Text Editor */}
          <Col xs={24}>
            <Form.Item name={"Comment"}>
              <SlateEditor 
        
            editorValue={editorValue}
            setEditorValue={setEditorValue}
              isShowUserSectionMention={true} uploadPcFolderPath="Ticket" watchListIds={ticketDetails.Watchlist || []} onChangeMention={handleMentionChange} readOnly={false} />
            </Form.Item>
          </Col>

          {/* File Manager */}
          <Col xs={24}>
            <ReusableFileManager
              fileList={fileList}
              onFileListChange={setFileList}
              title="Yorum Dosyaları"
              showCount={true}
              maxFiles={10}
              imageSize={{ width: 100, height: 100 }}
              cardSize="small"
              className="p-0"
              emptyMessage="Yorum için dosya eklemek istiyorsanız + butonuna tıklayın."
              showEmptyMessage={false} // Yorumda boş mesaj gösterme
              folderPath="ticket-comments" // Ticket yorumları için özel klasör
            />
          </Col>

          {/* Submit Button - Artık drawer başlığından tetikleniyor */}
          {(
            <Col xs={24}>
              <MazakaButton
                processType={formActions.submitProcessType}
                htmlType="button"
                onClick={async () => {
                  await form.validateFields();
                  handleOnFinish()
                }}
              >
                {t("ticket.list.save")}
              </MazakaButton>
            </Col>
          )}
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateComment;

