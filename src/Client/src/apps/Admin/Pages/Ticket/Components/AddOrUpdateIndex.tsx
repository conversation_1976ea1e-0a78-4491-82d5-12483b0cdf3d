import {   Tabs,  } from "antd";
import AddOrUpdateTicket from "./AddOrUpdateTicket";
import { TabsProps } from "antd/lib";
import ChatIndex from "@/apps/Chat/ChatIndex";
import { RootState } from "@/store/Reducers";
import { useDispatch, useSelector } from "react-redux";
import { hanldleSetSelectedUserListChatItem } from "@/apps/Chat/ClientSideStates";
import ShowNoteDetail from "@/apps/Call/Components/ShowNoteDetail";
import chatEndPoints from "@/apps/Chat/EndPoints"
import callEndPoints from "@/apps/Call/EndPoints"
import { useQueryClient } from "react-query";
import { useTranslation } from "react-i18next";






interface AddOrUpdateIndexProps {
  onSubmitSuccess?: () => void;
}

const AddOrUpdateIndex: React.FC<AddOrUpdateIndexProps> = ({ onSubmitSuccess }) => {
   const {  ticketDetails } = useSelector(
      (state: RootState) => state.ticket
    );
    const {t} = useTranslation()
 
  const dispatch = useDispatch()
  const queryClient = useQueryClient()
  const items: TabsProps['items'] = [
    
    {
      key: 'ticket',
      label: 'Ticket',
      children: <AddOrUpdateTicket onSubmitSuccess={onSubmitSuccess} />
     
    },
    ...(ticketDetails?.ChatId?[

      {
        key: 'chat',
        label:t("chat.chats"),
        children:<ChatIndex pageType="ticket" />
      
      },
    ]:[]),

    ...(ticketDetails?.CallId?[
      {
        key: 'call',
        label: t("customers.add.call"),
        children:<div className="!px-6">
        <ShowNoteDetail isShowUser={true} selectedRecord={{Id:ticketDetails?.CallId}} />
        </div>
      
      },

    ]:[]),

   
  ];
  
  

  return (
   
        <Tabs tabBarStyle={{marginLeft:"27px"}} className="" defaultActiveKey="1" items={items} onChange={(key:string)=>{
         
          if(key==="chat")
          {
        
            queryClient.resetQueries({
              queryKey: chatEndPoints.getChatMessageDetails,
              exact: false,
            });
           dispatch(hanldleSetSelectedUserListChatItem({ data: {Id:ticketDetails?.ChatId} }));
             
          }
          else if(key==="call")
          {
            queryClient.resetQueries({
              queryKey: callEndPoints.getCallDetail,
              exact: false,
            });
          }


        }}   />
       
        
     
  );
};

export default AddOrUpdateIndex;