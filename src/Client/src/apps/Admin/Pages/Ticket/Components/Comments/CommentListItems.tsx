import { RootState } from "@/store/Reducers";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useGetTicketComments } from "../../ServerSideStates";

import { Timeline, Avatar, Space, Row, Col, Pagination, Skeleton } from "antd";
import { UserOutlined } from "@ant-design/icons";
import SlateEditor from "@/apps/Common/Editor/SlateEditor";
import dayjs from "dayjs";
import PageTitle from "@/apps/Common/PageTitle";
import { useTranslation } from "react-i18next";
import FilePreview from "@/apps/Common/FilePreview";
import { Descendant } from "slate";

// Yorum stringini Slate formatına veya normal stringe parse eder
const parseCommentData = (comment: string | object) => {
  try {
    return typeof comment === "string" ? JSON.parse(comment) : comment;
  } catch (error) {
    console.error("Comment parse edilemedi:", error);
    return comment;
  }
};

const CommentListItems = () => {
  const { ticketDetails } = useSelector((state: RootState) => state.ticket);
  const { t } = useTranslation();

  const [editorValue, setEditorValue] = useState<Descendant[]>([
    { type: "paragraph", children: [{ text: "" }] },
  ]);

  const [filter, setFilter] = useState({
    PageNumber: 1,
    PageSize: 20,
    TicketId: ticketDetails?.Id,
  });

  useEffect(() => {
    setFilter((prev) => ({ ...prev, TicketId: ticketDetails?.Id }));
  }, [ticketDetails?.Id]);

  const comments = useGetTicketComments(filter);
  const commentData = comments?.data?.Value ?? [];

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    setFilter((prev) => ({ ...prev, PageNumber: pageNum, PageSize: pageSize }));
  };

  const renderComment = (rawComment: any) => {
    const parsed = parseCommentData(rawComment);

    if (Array.isArray(parsed) && parsed.every((n) => n.type && Array.isArray(n.children))) {
      return (
        <SlateEditor
          readOnly
          watchListIds={[ticketDetails?.Watchlist]}
          isShowUserSectionMention={false}
          showToolbar={false}
          showMentions={false}
          uploadPcFolderPath="Ticket"
          placeholder=""
          className="!border-0 !shadow-none !bg-transparent !p-0 !m-0"
          editorValue={parsed}
          setEditorValue={setEditorValue}
        />
      );
    }

    if (typeof parsed === "string") {
      return <div dangerouslySetInnerHTML={{ __html: parsed }} />;
    }

    return <div>{String(parsed)}</div>;
  };

  return (
    <Row>
      <Skeleton loading={comments.isLoading || comments.isFetching}>
        {commentData.length > 0 && (
          <>
            <Col xs={24}>
              <PageTitle title={t("ticket.list.comments")} isSubTitle />
            </Col>

            <Col xs={24}>
              <Timeline
                className="!mt-4"
                mode="left"
                items={commentData.map((item: any) => ({
                  children: (
                    <Space align="start">
                      <Avatar className="!w-[25px] !h-[25px]" icon={<UserOutlined />} />
                      <div>
                        <div className="!text-xs !text-gray-500">
                          {dayjs(item.InsertDate).format("DD.MM.YYYY HH:mm")}
                        </div>
                        <div className="!text-xs font-medium">{item.UserName}</div>
                        <div style={{ marginTop: 4, lineHeight: "1.4" }}>
                          {renderComment(item.Comment)}
                        </div>

                        {item.Files?.length > 0 && (
                          <div style={{ marginTop: 8 }}>
                            <FilePreview
                              files={item.Files}
                              title="Ekli Dosyalar"
                              showCount
                              imageSize={{ width: 60, height: 60 }}
                              maxWidth="450px"
                              gap="6px"
                              showFileNames
                              showTitle
                            />
                          </div>
                        )}
                      </div>
                    </Space>
                  ),
                }))}
              />
            </Col>

            <Col xs={24} className="!flex justify-end">
              <Pagination
                className="!px-0"
                onChange={handleChangePagination}
                total={comments.data?.FilteredCount || 0}
                current={comments.data?.PageNumber}
                pageSize={comments.data?.PageSize}
                showLessItems
                size="small"
                showSizeChanger
                locale={{ items_per_page: "" }}
                showTotal={(total) => `${total}`}
              />
            </Col>
          </>
        )}
      </Skeleton>
    </Row>
  );
};

export default CommentListItems;
