import { MazakaButton } from "@/apps/Common/MazakaButton";
import { MazakaForm } from "@/apps/Common/MazakaForm";
import { MazakaInput } from "@/apps/Common/MazakaInput";
import { openNotificationWithIcon } from "@/helpers/OpenNotificationWithIcon";
import useMazakaForm from "@/hooks/useMazakaForm";
import { Col, Form, Row, Switch } from "antd";
import { FC, useEffect, useState } from "react";
import { useQueryClient } from "react-query";

import endPoints from "../EndPoints";
import { t } from "i18next";
import { showErrorCatching } from "@/helpers/ShowErrorCatching";
import { createTemplate, updateTemplateWithPut } from "../Services";
import { MazakaSelect } from "@/apps/Common/MazakaSelect";
import SlateEditor from "@/apps/Common/Editor/SlateEditor";
import Variables from "./Variables";
import { MazakaTextArea } from "@/apps/Common/MazakaTextarea";
import { determineTemplateType } from "@/helpers/Template";
import GeneralLanguage from "@/apps/Common/GeneralLanguage";
import { Descendant } from "slate";

const AddOrUpdateTemplate: FC<{
  onFinish: () => void;
  selectedRecord?: any;
}> = ({ onFinish, selectedRecord }) => {
  const queryClient = useQueryClient();
  const [editorValue, setEditorValue] = useState<Descendant[]>([
    {
      type: "paragraph",
      children: [{ text: "" }],
    },
  ]);
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const [selectPlatform, setSelectPlatform] = useState<null | number>(null);
  const templateType = determineTemplateType("select", t);
  const [selectedSubject, setSelectedSubject] = useState<number | null>(null);
  const subjectOptions = [
    { label: t("template.passwordRest"), value: 1 },
    { label: t("template.flow"), value: 2 },
    { label: t("template.pause"), value: 3 },
  ];

  useEffect(() => {
    if (selectedRecord) {
      let data = { ...selectedRecord };
      if (data["TemplateType"] === "Email") {
        data["Content"] = data["Content"] ? JSON.parse(data["Content"]) : [];
        setEditorValue(data["Content"]);
      } else {
        setEditorValue([]);
      }

      if (data["Subject"] === "PasswordReset") {
        data["Subject"] = 1;
        setSelectedSubject(1);
      } else if (data["Subject"] === "Flow") {
        data["Subject"] = 2;
        setSelectedSubject(2);
      }
      if (data["Subject"] === "Pause") {
        data["Subject"] = 3;
        setSelectedSubject(3);
      }

      if (selectedRecord?.TemplateType === "Email") {
        setSelectPlatform(1);
        data["TemplateType"] = 1;
      } else if (selectedRecord?.TemplateType === "Sms") {
        setSelectPlatform(2);
        data["TemplateType"] = 2;
      } else if (selectedRecord?.TemplateType === "MobilePush") {
        setSelectPlatform(3);
        data["TemplateType"] = 3;
      } else {
        setSelectPlatform(4);
        data["TemplateType"] = 4;
      }

      form.setFieldsValue({ ...data });
    }
  }, [selectedRecord]);

  const hangleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();

    if (selectPlatform === 1) {
      formValues["Content"] = JSON.stringify(formValues["Content"]);
    }

    try {
      if (selectedRecord) {
        await updateTemplateWithPut({ ...selectedRecord, ...formValues });
      } else {
        await createTemplate(formValues);
      }
      openNotificationWithIcon("success", t("form.transactionSuccessful"));
      mazakaForm.setSuccess(2000, () => t("form.transactionSuccessful"));
      form.resetFields();
      onFinish();
      queryClient.resetQueries({
        queryKey: endPoints.getTemplateListFilter,
        exact: false,
      });
      setEditorValue([])
    } catch (error) {
      showErrorCatching(error, mazakaForm, true, t);
    }
  };

  return (
    <>
      <MazakaForm
        form={form}
        onFinish={hangleOnFinish}
        submitButtonVisible={false}
        initialValues={{ Active: true }}
      >
        <Row gutter={[20, 10]}>
          <Col xs={24} xl={selectedSubject ? 16 : 24}>
            <Row gutter={[10, 10]}>
              <MazakaInput
                label={t("template.name")}
                placeholder={t("template.name")}
                xs={24}
                md={12}
                xl={8}
                name="Name"
                rules={[{ required: true, message: "" }]}
              />
              <MazakaSelect
                label={t("template.platform")}
                placeholder={t("template.platform")}
                name="TemplateType"
                rules={[{ required: true, message: "" }]}
                options={templateType}
                xs={24}
                md={12}
                xl={8}
                onChange={(value: number) => {
                  setSelectPlatform(value);
                  form.resetFields(["Content"]);
                }}
              />
              <GeneralLanguage
                label={t("template.language")}
                placeholder={t("template.language")}
                name="Language"
                xs={24}
                md={12}
                xl={8}
              />
              <MazakaSelect
                label={t("template.title")}
                placeholder={t("template.title")}
                xs={24}
                name="Subject"
                rules={[{ required: true, message: "" }]}
                options={subjectOptions}
                xl={12}
                onChange={(value: number) => {
                  setSelectedSubject(value);
                }}
              />
              <Col xs={24} xl={12}>
                <Form.Item name="Active" label={t("template.status")}>
                  <Switch />
                </Form.Item>
              </Col>
              {selectPlatform && selectPlatform !== 1 && (
                <MazakaTextArea
                  xs={24}
                  rows={8}
                  label={t("template.description")}
                  placeholder={t("template.description")}
                  name={"Content"}
                  rules={[{ required: true, message: "" }]}
                />
              )}
            </Row>
          </Col>
          {selectedSubject && (
            <Col xs={24} xl={8}>
              <Variables selectedSubject={selectedSubject} />
            </Col>
          )}

          {selectPlatform === 1 && (
            <Col xs={24}>
              <Form.Item
                name={"Content"}
                rules={[{ required: true, message: "" }]}
              >
                <SlateEditor
                  isShowUserSectionMention={false}
                  uploadPcFolderPath="Template"
                  watchListIds={[]}
                  readOnly={false}
                  editorValue={editorValue}
                  setEditorValue={setEditorValue}
                />
              </Form.Item>
            </Col>
          )}

          <Col xs={24}>
            <MazakaButton
              processType={formActions.submitProcessType}
              htmlType="submit"
              status="save"
            >
              {selectedRecord ? t("template.edit") : t("template.add")}
            </MazakaButton>
          </Col>
        </Row>
      </MazakaForm>
    </>
  );
};

export default AddOrUpdateTemplate;
