import { Route } from "react-router-dom";
import AddOrUpdateOtoDailerIndex from "./Pages/AutoDialer/Components/AddOrUpdate/AddOrUpdateIndex";
import AdminIndex from "./AdminIndex";
import ImportDataIndex from "./Pages/TempCustomer/TempCustomerIndex";
import AddOrUpdateImportDataIndex from "./Pages/TempCustomer/AddOrUpdate/AddTempCustomerIndex";
import OtoDailerIndex from "./Pages/AutoDialer/AutoDialerIndex";
import UsersIndex from "./Pages/Users/<USER>";
import AddOrUpdateUserIndex from "./Pages/Users/<USER>/AddOrUpdate.tsx/AddOrUpdateUserIndex";
import CustomerIndex from "./Pages/Customers/CustomerIndex";
import AddOrUpdateCustomerIndex from "./Pages/Customers/Components/AddOrUpdate/AddOrUpdateCustomerIndex";
import SectorIndex from "./Pages/Sector/SectorIndex";
import WorkflowIndex from "./Pages/WorkFlow/WorkflowIndex";
import AddOrUpdateWorkflowIndex from "./Pages/WorkFlow/Components/AddOrUpdate/AddOrUpdateWorkflowIndex";
import ReportIndex from "./Pages/Report/ReportIndex";
import MyReportIndex from "./Pages/Report/Pages/MyReport/MyReportIndex";
import ReportTypesIndex from "./Pages/Report/Pages/ReportTypes/ReportTypesIndex";
import AddOrUpdateReprotTypes from "./Pages/Report/Pages/ReportTypes/Components/AddOrUpdate/AddOrUpdateReportTypesIndex";
import RecordingsIndex from "./Pages/Recordings/RecordingIndex";
import TaskIndex from "./Pages/Task/TaskIndex";
import DepartmentIndex from "./Pages/Department/DepartmentIndex";
import AddOrUpdateAuthorityIndex from "./Pages/Authority/Components/AddOrUpdate/AddOrUpdateAuthorityIndex";
import ProfessionIndex from "./Pages/Profession/ProfessionIndex";
import ClassificationIndex from "./Pages/Classification/ClassificationIndex";
import ThreeCXQueuesIndex from "./Pages/ThreeCXQueues/ThreeCXQueuesIndex";
import RoleIndex from "./Pages/Role/RoleIndex";
import SubjectTicketIndex from "./Pages/TicketSubject/SubjectTicketIndex";
import TicketIndex from "./Pages/Ticket/TicketIndex";
import LanguageIndex from "../Language/LanguageIndex";
import PausesIndex from "../Pauses/PausesIndex";
import CallIndex from "../Call/CallIndex";
import NotificationWayIndex from "./Pages/NotificationWay/NotificationWayIndex";
import LogsIndex from "./Pages/AuditLogs/LogsIndex";
import PauseTypeIndex from "./Pages/PauseType/PauseTypeIndex";
import CustomerSourceIndex from "./Pages/CustomerSource/CustomerSourceIndex";
import NotesIndex from "./Pages/Notes/NotesIndex";
import FileManagerIndex from "../FileManager/FileManagerIndex";
import FormIndex from "../Form/FormIndex";
import FormAttrIndex from "../Form/Components/FormAttr/FormAttrIndex";
import TemplateIndex from "./Pages/Template/TemplateIndex";
import CalendarNotesIndex from "./Pages/Calendar/CalendarNotesIndex";






const panelPrefix = "/panel";

const panelRoutes = [
  { path: "users", element: <UsersIndex /> },
  { path: "add-user", element: <AddOrUpdateUserIndex /> },
  { path: "edit-user/:userId", element: <AddOrUpdateUserIndex /> },

  { path: "customers", element: <CustomerIndex /> },
  { path: "add-customer", element: <AddOrUpdateCustomerIndex type="url" /> },
  { path: "edit-customer/:customerId", element: <AddOrUpdateCustomerIndex type="url" /> },

  { path: "import-data", element: <ImportDataIndex /> },
  { path: "add-import-data", element: <AddOrUpdateImportDataIndex pageType="url" isAutoDialer={false} /> },
  { path: "edit-temp-customer", element: <AddOrUpdateCustomerIndex type="url" /> },

  { path: "auto-dailer", element: <OtoDailerIndex /> },
  { path: "edit-auto-dialer/:autoDialerId", element: <AddOrUpdateOtoDailerIndex /> },
  { path: "add-auto-dialer", element: <AddOrUpdateOtoDailerIndex /> },

  { path: "workflow", element: <WorkflowIndex /> },
  { path: "edit-workflow/:workFlowId", element: <AddOrUpdateWorkflowIndex /> },

  { path: "logs", element: <LogsIndex /> },
  { path: "pauses", element: <PausesIndex role="admin" /> },

  { path: "pause-types", element: <PauseTypeIndex /> },
  { path: "customer-sources", element: <CustomerSourceIndex /> },
  { path: "notification-ways", element: <NotificationWayIndex /> },
  { path: "call-reports", element: <CallIndex /> },
  { path: "reports", element: <ReportIndex /> },
  { path: "recordings", element: <RecordingsIndex /> },
  { path: "reprort/my-report", element: <MyReportIndex /> },
  { path: "reprort/report-types", element: <ReportTypesIndex /> },
  { path: "professions", element: <ProfessionIndex /> },
  { path: "file-manager", element: <FileManagerIndex onFinishSelectFile={()=>{}} isShowTitle={true} /> },
  { path: "languages", element: <LanguageIndex /> },
  { path: "roles", element: <RoleIndex /> },
  { path: "subject-ticket", element: <SubjectTicketIndex /> },
  { path: "sectors", element: <SectorIndex /> },
  { path: "templates", element: <TemplateIndex /> },
  { path: "calendar-notes", element: <CalendarNotesIndex /> },
  { path: "forms", element: <FormIndex/> },
  { path: "form-attr-details/:formId", element: <FormAttrIndex/> },
  { path: "classifications", element: <ClassificationIndex /> },
  { path: "user-department", element: <DepartmentIndex /> },
  { path: "tickets", element: <TicketIndex mode="customer" pageType="ticket" /> },
  { path: "threecxqueues", element: <ThreeCXQueuesIndex /> },
  { path: "notes", element: <NotesIndex /> },
  { path: "tasks", element: <TaskIndex /> },
  { path: "authority", element: <AddOrUpdateAuthorityIndex /> },
  { path: "report/add-report-types", element: <AddOrUpdateReprotTypes /> },
  
];

export const panelRouteList = (
  <Route key="panelRouteList">
    <Route path={panelPrefix} element={<AdminIndex />}>
      {panelRoutes.map((route, idx) => (
        <Route
          key={idx}
          path={`${panelPrefix}/${route.path}`}
          element={route.element}
        />
      ))}
    </Route>
  </Route>
);


