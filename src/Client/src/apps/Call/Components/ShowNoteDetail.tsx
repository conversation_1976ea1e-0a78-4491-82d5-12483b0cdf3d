
import { useTranslation } from "react-i18next";
import { Col, Skeleton, Table, Typography } from "antd";
import { FC } from "react";
import { useGetCallDetails } from "../ServerSideStates";
import dayjs from "dayjs"

const { Text } = Typography;

const ShowNoteDetail: FC<{ selectedRecord: null | any,isShowUser?:boolean }> = ({ selectedRecord,isShowUser=false }) => {
  const { t } = useTranslation();

  const callDetails = useGetCallDetails(selectedRecord?.Id)



  const columns = [
    {
      title: t("notes.note"),

      render: (_: string, call: any) => {
        return (
          <>
            <Text className="!text-xs">{`${call?.Content || ""} `}</Text>
          </>
        );
      },
    },
    ...(isShowUser?[
      {
        title: t("customers.add.createdUser"),
       

        render: (value: string,) => {
          return (
            <>
              <Text className="!text-xs">{callDetails.data?.Value?.User||""}</Text>
            </>
          );
        },
      },

    ]:[]),
    
    {
      title: t("notes.insertDate"),

      render: (_: string, call: any) => {
        return (
          <>
            <Text className="!text-xs">{`${dayjs(call?.InsertDate).format("DD.MM.YYYY HH:mm") || ""} `}</Text>
          </>
        );
      },
    }
  ];



  return (
    <>
      <Table
        columns={columns}
        loading={callDetails.isLoading || callDetails.isFetching}
        dataSource={callDetails?.data?.Value?.Notes}
      // pagination={{
      //   position: ["bottomRight"],
      //   className: "!px-0",
      //   total: callDetails.data?.Value?.length || 0,
      //   current: 1,
      //   pageSize: 30,
      //   showLessItems: true,
      //   size: "small",
      //   showSizeChanger: true,
      //   locale: { items_per_page: "" },
      //   showTotal: (e) => `${e}`,
      // }}
      />
    </>

  );
};

export default ShowNoteDetail;