using System.Text.Json;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Requests.Application.Abstractions;
using Requests.Application.Rules.Engine;
using Requests.Domain;
using Shared.Application;
using Shared.Application.EventBus;
using Shared.Domain;

namespace Requests.Application.Transitions.ExecuteTransition;

public class ExecuteTransitionCommandHandler(
    IRequestsDbContext context,
    IRuleEngine ruleEngine,
    IWorkContext workContext,
    IEventBus eventBus,
    ILogger<ExecuteTransitionCommandHandler> logger) : IRequestHandler<ExecuteTransitionCommand, Result<ExecuteTransitionResult>>
{
    public async Task<Result<ExecuteTransitionResult>> Handle(
        ExecuteTransitionCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            // 1. Transition bilgilerini al
            var transition = await context.Transitions
                .Include(t => t.FromNode)
                .Include(t => t.ToNode)
                .Include(t => t.Rules.Where(r => r.IsActive))
                .FirstOrDefaultAsync(t => t.Id == request.TransitionId, cancellationToken);

            if (transition == null)
            {
                return Result.Failure<ExecuteTransitionResult>("Transition bulunamadı");
            }

            // 2. Ticket'ın var olduğunu kontrol et
            var ticket = await context.Tickets
                .Include(x => x.Subject)
                .FirstOrDefaultAsync(t => t.Id == request.TicketId, cancellationToken);

            if (ticket == null)
            {
                return Result.Failure<ExecuteTransitionResult>("Ticket bulunamadı");
            }
            if (ticket.Subject?.FlowId != transition.ToNode.FlowId)
            {
                return Result.Failure<ExecuteTransitionResult>("Flowlar eşleşmiyor");
            }

            // 3. Rich Context oluştur
            var transitionContext = await CreateTransitionContextAsync(
                ticket,
                transition,
                request.AdditionalData,
                cancellationToken);

            // 4. Rule Engine ile kuralları çalıştır
            var ruleResult = await ruleEngine.ExecuteRulesAsync(
                request.TransitionId,
                transitionContext,
                cancellationToken);

            if (!ruleResult.IsSuccess)
            {
                return Result.Failure<ExecuteTransitionResult>(
                    string.Join(", ", ruleResult.Errors));
            }

            // 5. Ticket'i güncelle
            await UpdateTicketAsync(
                ticket,
                transition.ToNode.Id,
                ruleResult.ModifiedFields,
                cancellationToken);

            // 6. Bildirimleri gönder (şimdilik log)
            foreach (var notification in ruleResult.PendingNotifications)
            {
                logger.LogInformation("Notification queued: {Type} to {RecipientCount} recipients",
                    notification.Type, notification.Recipients.Count);
                foreach (var recipient in notification.Recipients)
                {
                    var jsonString = JsonSerializer.Serialize(notification.Data);
                    await eventBus.PublishAsync(new NotificationSendedEvent(
                        recipient,
                        notification.Subject,
                        notification.Template,
                        notification.Type,
                        jsonString
                    ), cancellationToken);
                }
            }
            foreach (var recipient in ticket.Watchlist)
            {
                await eventBus.PublishAsync(new NotificationSendedEvent(
                                recipient,
                                "Durum Değişti",
                                ticket.Code + " kodlu ticket durumu değişti.",
                                "TicketStatus",
                                ticket.Id.ToString()
                            ), cancellationToken);
            }

            return Result.Success(new ExecuteTransitionResult
            {
                TransitionId = request.TransitionId,
                NewNodeId = transition.ToNodeId,
                ModifiedFields = ruleResult.ModifiedFields,
                NotificationsSent = ruleResult.PendingNotifications.Count,
                ExecutedRules = ruleResult.ExecutedRuleCount
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Transition execution failed for ticket {TicketId} and transition {TransitionId}",
                request.TicketId, request.TransitionId);
            return Result.Failure<ExecuteTransitionResult>("Transition çalıştırma sırasında hata oluştu");
        }
    }

    private async Task<TransitionContext> CreateTransitionContextAsync(
        Ticket ticket,
        Transition transition,
        Dictionary<string, object>? additionalData,
        CancellationToken cancellationToken)
    {
        var ticketData = new Dictionary<string, object>();
        if (ticket != null)
        {
            ticketData["Id"] = ticket.Id;
            ticketData["Title"] = ticket.Title;
            ticketData["Description"] = ticket.Description;
            ticketData["Priority"] = ticket.Priority.ToString();
            ticketData["StatusId"] = ticket.StatusId;
            ticketData["CustomerId"] = ticket.CustomerId;
            ticketData["UserId"] = ticket.UserId;
            ticketData["ReporterUserId"] = ticket.ReporterUserId;
        }
        var user = await workContext.GetUserAsync();
        var userData = new Dictionary<string, object>
        {
            ["Id"] = user.Id,
            ["Name"] = user.Fullname ?? "Bilinmeyen Kullanıcı"
        };

        return new TransitionContext
        {
            TicketId = ticket.Id,
            UserId = workContext.UserId,
            TicketData = ticketData,
            UserData = userData,
            Metadata = additionalData ?? [],
            FromNode = transition.FromNode,
            ToNode = transition.ToNode,
            TransitionTime = DateTime.UtcNow
        };
    }

    private async Task UpdateTicketAsync(
        Ticket ticket,
        Guid newNodeId,
        Dictionary<string, object> modifiedFields,
        CancellationToken cancellationToken)
    {
        // StatusId'yi güncelle (node'un ID'si status olarak kullanılıyor)
        ticket.StatusId = newNodeId;

        // Modified fields'ları uygula
        foreach (var field in modifiedFields)
        {
            switch (field.Key.ToLower())
            {
                case "title":
                    ticket.Title = field.Value?.ToString() ?? ticket.Title;
                    break;
                case "description":
                    ticket.Description = field.Value?.ToString() ?? ticket.Description;
                    break;
                case "priority":
                    if (Enum.TryParse<PriorityEnum>(field.Value?.ToString(), out var priority))
                    {
                        ticket.Priority = priority;
                    }
                    break;
                case "assigneduserid":
                    if (Guid.TryParse(field.Value?.ToString(), out var assigneduserid))
                    {
                        ticket.UserId = assigneduserid;
                    }
                    break;
                case "assigneddepartmentid":
                    if (Guid.TryParse(field.Value?.ToString(), out var assigneddepartmentid))
                    {
                        var existingDepartment = ticket.TicketDepartment.FirstOrDefault(td => td.DepartmentId == assigneddepartmentid);
                        if (existingDepartment != null)
                        {
                            continue;
                        }
                        ticket.TicketDepartment.Add(new TicketDepartment { DepartmentId = assigneddepartmentid });
                    }
                    break;
                case "status":
                    if (Guid.TryParse(field.Value?.ToString(), out var statusId))
                    {
                        ticket.StatusId = statusId;
                    }
                    break;
                case "enddate":
                    if (DateTime.TryParse(field.Value?.ToString(), out var endDate))
                    {
                        ticket.EndDate = endDate;
                    }
                    break;
            }
        }
        await context.SaveChangesAsync(cancellationToken);
    }
}
