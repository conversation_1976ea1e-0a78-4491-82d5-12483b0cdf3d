namespace Requests.Application.Rules.Engine.Configurations;

public class NotificationConfiguration
{
    public NotificationType NotificationType { get; set; }
    public string Template { get; set; } = string.Empty;
    public UserType UserType { get; set; }
    public List<string>? Users { get; set; }

}

public enum UserType
{
    User = 1,
    Customer = 2
}

public class RecipientDefinition
{
    public RecipientType Type { get; set; }
    public string? FieldName { get; set; }
    public List<string>? Roles { get; set; }
    public List<string>? Users { get; set; }
}

public enum NotificationType
{
    Email = 1,
    SMS = 2,
    InApp = 3,
    Push = 4
}

public enum RecipientType
{
    Field = 1,      // Ticket'taki bir alandan alınacak
    Role = 2,       // Bel<PERSON>li rollerdeki kullanıcılar
    User = 3,       // Belirli kullanıcılar
    CurrentUser = 4 // Mevcut kullanıcı
}
