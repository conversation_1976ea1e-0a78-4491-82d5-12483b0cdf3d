using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Users.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class UsersData020 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("0409cc45-ea3f-41ca-8955-77af6ec09a9c"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("042472cf-e56c-4f3f-91c4-a8d60bd03577"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("097f44e8-525f-4251-b786-539ba9dc63fe"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2153883d-066a-4927-bdb5-3352fa75a0e2"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("221a7f49-ac18-4729-9799-885f7be3d114"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("23c4656b-b3c3-4753-84fd-53ba895a26e8"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("277699ef-e8b0-409c-ba88-3a23ee837770"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("28f5febe-e619-4292-b909-5055c273eb8c"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2d3755db-5f7f-4e79-aa71-129dbda6245f"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3448831d-e56d-49ba-99a8-136ee4bbba66"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("38934ec5-cb00-4cb7-ac72-c64631e96586"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3acd3e0d-b950-4312-a1fe-d71259950829"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3b0d9186-3c64-4b76-afef-f2ff9804738c"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3c600b41-8152-496d-9a96-9102af74e3cd"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("3c858a87-df1b-4bd0-82e5-48905739731f"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("429a1097-2016-40f5-abec-1d879d86ca56"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("42b5022c-ed69-452c-b035-9e4c7925e156"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5ca572dc-dd8b-4e51-9ef8-11faa4049a81"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("64ca878d-b9b6-4e2d-b176-3686778dd12c"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("65ba4543-69d5-404b-9deb-f8c72953fcde"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6f297895-c78a-486c-947c-e2e1de1d7e3f"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7fc74060-b381-4390-8db5-5923d7b605b1"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("87f0e782-9020-44c5-bcd8-1bc125e69102"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8b3c21c1-badc-4243-a1cb-10a39208d85a"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("96f554d7-bd72-4868-92f0-f6e8ec9953d6"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a8d5c7e4-1e97-4a6b-b3ee-7cdf008ba7ab"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("c1072a1a-22cc-42bf-a830-841025d6fe48"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cbb4ca38-8551-4559-812d-97d45a477195"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("dd51494b-e597-4b7e-9ed0-945ec7cc601b"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("eaa2aa2b-1ae4-44b9-9a1c-c900cb931911"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("f7376551-0714-4f86-a40b-7cb46c67529b"));

            migrationBuilder.AddColumn<string>(
                name: "Description",
                schema: "Users",
                table: "Permission",
                type: "nvarchar(2048)",
                maxLength: 2048,
                nullable: true);

            migrationBuilder.InsertData(
                schema: "Users",
                table: "Permission",
                columns: new[] { "Id", "Description", "Icon", "IsMenu", "Key", "Name", "Order", "TopPermissionId", "Url" },
                values: new object[,]
                {
                    { new Guid("1ae83117-1a56-45c8-a80c-51634fea7a70"), "Departman Yönetimi", null, false, "Users.Departments", "Departman Yönetimi", 0, null, null },
                    { new Guid("1d76f6a9-5d91-4d78-92b9-231e5c3c7b67"), "İzin Yönetimi", null, false, "Users.Permissions", "İzin Yönetimi", 0, null, null },
                    { new Guid("24d7e4f0-c446-486b-b45f-9d2e02012a3b"), "Mola Yönetimi", null, false, "Users.Pauses", "Mola Yönetimi", 0, null, null },
                    { new Guid("3b387098-cdf4-4a51-888e-1f4c8cb78187"), "Kullanıcı Yönetimi", null, false, "Users.Management", "Kullanıcı Yönetimi", 0, null, null },
                    { new Guid("3ce23abe-e9d0-4a4a-9af3-961e0c35c07d"), "Müşteri Kaynağı Yönetimi", null, false, "Customers.Sources", "Müşteri Kaynağı Yönetimi", 0, null, null },
                    { new Guid("41142ba2-98f6-4fc4-8cf0-a01c48ae3f68"), "Çağrı Yönetimi", null, false, "Conversations.Calls", "Çağrı Yönetimi", 0, null, null },
                    { new Guid("4d0176c7-fc7c-47b1-94a0-3e29a6ebd4b4"), "Görev Yönetimi", null, false, "Tasks.Management", "Görev Yönetimi", 0, null, null },
                    { new Guid("58e2bf4c-b6a7-4acf-8c98-65214e35492f"), "Dinamik Form Veri Yönetimi", null, false, "DynamicForms.Records", "Dinamik Form Veri Yönetimi", 0, null, null },
                    { new Guid("5eba7ab2-3157-4f1a-a33c-e4d9425c5c0b"), "Müşteri İletişim Yönetimi", null, false, "Customers.Contacts", "Müşteri İletişim Yönetimi", 0, null, null },
                    { new Guid("5ebc3fa3-4705-41e0-a9b4-0fa92e717a21"), "Ülke Yönetimi", null, false, "General.Countries", "Ülke Yönetimi", 0, null, null },
                    { new Guid("68a6c7c5-213e-48b0-b85f-4d6f59a9d294"), "Otomatik Çağrı Yönetimi", null, false, "Conversations.Autodialers", "Otomatik Çağrı Yönetimi", 0, null, null },
                    { new Guid("78f1e171-e9f5-4daa-b5c8-31e42b7c3d0d"), "Müşteri Adres Yönetimi", null, false, "Customers.Addresses", "Müşteri Adres Yönetimi", 0, null, null },
                    { new Guid("7f39ce9d-918c-4caa-bc3d-056da79f9320"), "Ticket Yorum Yönetimi", null, false, "Requests.TicketComments", "Ticket Yorum Yönetimi", 0, null, null },
                    { new Guid("8bc4c493-7707-43f5-b32d-01a4e162192a"), "Görev Raporlama", null, false, "Tasks.Reports", "Görev Raporlama", 0, null, null },
                    { new Guid("93a7df0c-1d63-4b40-89c8-dc25b83cb438"), "Dinamik Form Yönetimi", null, false, "DynamicForms.Management", "Dinamik Form Yönetimi", 0, null, null },
                    { new Guid("9bb53de9-8256-4be8-b91e-fd7913232fbc"), "Çağrı Notları Yönetimi", null, false, "Conversations.CallNotes", "Çağrı Notları Yönetimi", 0, null, null },
                    { new Guid("9e5c0144-0d94-4d69-9893-39ef56fbd3fa"), "Sohbet Yönetimi", null, false, "Conversations.Chats", "Sohbet Yönetimi", 0, null, null },
                    { new Guid("a0928d14-d790-447b-8a2a-4f3bcd8660dd"), "Dil Yönetimi", null, false, "General.Language", "Dil Yönetimi", 0, null, null },
                    { new Guid("a5cc8622-e79d-4d76-9842-017f364d4d67"), "Müşteri Yönetimi", null, false, "Customers.Management", "Müşteri Yönetimi", 0, null, null },
                    { new Guid("adc6418a-fa24-4918-b308-bb8765a31dbd"), "Rol Yönetimi", null, false, "Users.Roles", "Rol Yönetimi", 0, null, null },
                    { new Guid("c1dfca5c-6d4a-4d13-9a63-5524b537ef5c"), "Dinamik Form Alan Yönetimi", null, false, "DynamicForms.Attributes", "Dinamik Form Alan Yönetimi", 0, null, null },
                    { new Guid("c3ef5138-a30a-4ce7-9eb6-b7363fd56821"), "Müşteri Mesleği Yönetimi", null, false, "Customers.Professions", "Müşteri Mesleği Yönetimi", 0, null, null },
                    { new Guid("c70c8ae2-c58e-4e21-8f7a-fd6e077be9a9"), "Görev Yorum Yönetimi", null, false, "Tasks.Comments", "Görev Yorum Yönetimi", 0, null, null },
                    { new Guid("c9f2f786-9e0a-4e37-b18a-6b17e4e3d5c4"), "Klasör Yönetimi", null, false, "General.Folders", "Klasör Yönetimi", 0, null, null },
                    { new Guid("cff5e347-2696-48a8-96f6-42f32b644233"), "Eyalet/İl Yönetimi", null, false, "General.States", "Eyalet/İl Yönetimi", 0, null, null },
                    { new Guid("dc42c9df-8541-4636-a55c-628b0910bf97"), "Ticket Subject Yönetimi", null, false, "Requests.TicketSubjects", "Ticket Subject Yönetimi", 0, null, null },
                    { new Guid("df0e33eb-a47c-4a30-bc66-d9e892b82355"), "Müşteri Sınıflandırma Yönetimi", null, false, "Customers.Classifications", "Müşteri Sınıflandırma Yönetimi", 0, null, null },
                    { new Guid("df6d8a28-1154-4eda-81f8-4875d44706cf"), "Şehir/İlçe Yönetimi", null, false, "General.Cities", "Şehir/İlçe Yönetimi", 0, null, null },
                    { new Guid("e3be97f7-d78c-4d7a-aee7-d58c1db10b0d"), "Ticket Yönetimi", null, false, "Requests.Tickets", "Ticket Yönetimi", 0, null, null },
                    { new Guid("eea6af3b-c9a0-4c26-a587-31f4b0dc6686"), "Müşteri Sektör Yönetimi", null, false, "Customers.Sectors", "Müşteri Sektör Yönetimi", 0, null, null },
                    { new Guid("f3c93e7b-2b1b-48a4-a0dd-e8bb4fe49e89"), "Dosya Yönetimi", null, false, "General.Files", "Dosya Yönetimi", 0, null, null }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("01f5af0e-b82f-4675-afa7-576e7277ea19"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("035b6bbb-93b1-45e2-ba97-6621dcdedb0d"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("130b23b8-b73b-457f-a06a-b96863cdc156"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("17c57302-d0c9-48af-9ae8-c51cea6b96ac"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("27f50dd5-07aa-47b9-8846-52efbfdd11e3"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("2ea084ab-8de7-4bc1-a401-a8fe98f00e87"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("36543221-4acb-4ca1-bb18-6601a86c52c8"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("44581323-896f-41f2-baad-8abeedcc831f"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("44d78807-6a15-4c85-a728-b3515ba43ada"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("47aa3572-2c24-454e-b13f-fbbef8cddd33"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4a4f095d-54bb-4c7b-bdb2-6bc09ce0d903"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("4b55a3a2-d374-4040-9328-5810d49cd12c"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("*************-49f9-aa38-8bc559b348cf"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("53a6ecc8-7ccc-4b9f-a85c-28b35ea5f4a5"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5902d23c-68d8-47b0-b7f3-f38c36969dfa"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("5fa9ff2e-9155-4958-b06f-************"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6aa0aaeb-5ad4-466a-8950-93b2567c0890"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("6eb7fa20-223a-4e1d-894b-ef2835c556ac"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("7812f101-54b9-41fa-95d4-6ea552d05df1"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8094ea43-07ba-40a7-9c89-d3373f923fb6"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("8d20e410-5f50-46dd-8deb-5c3597f355de"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9f813c13-f536-433f-95a6-3da8b9a2b975"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("9feacbd8-3407-4309-be46-9a470fbf9893"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("a50be7d0-9fbe-4a30-aa87-daed8b282b27"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("b0a6349b-c639-4f07-95e6-794b19a38514"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("bd939fa0-6072-4d81-9471-204f2cbd97a4"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("bfb1b5b0-6392-40dc-81ab-ec91e2b86571"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cc82a1d7-63f7-4d60-9e20-0ab81479fa42"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("cf164759-a963-46fc-9eae-a94b38ff20a1"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("e2372bf8-0bea-48cc-87c8-9615ce6861ec"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Permission",
                keyColumn: "Id",
                keyValue: new Guid("ee4ac140-9791-4d2f-b8d8-3ba6574a55a4"));

            migrationBuilder.InsertData(
                schema: "Users",
                table: "Permission",
                columns: new[] { "Id", "Icon", "IsMenu", "Key", "Name", "Order", "TopPermissionId", "Url" },
                values: new object[,]
                {
                    { new Guid("0409cc45-ea3f-41ca-8955-77af6ec09a9c"), null, false, "Customers.Contacts", "Müşteri İletişim Yönetimi", 0, null, null },
                    { new Guid("042472cf-e56c-4f3f-91c4-a8d60bd03577"), null, false, "General.Folders", "Klasör Yönetimi", 0, null, null },
                    { new Guid("097f44e8-525f-4251-b786-539ba9dc63fe"), null, false, "General.States", "Eyalet/İl Yönetimi", 0, null, null },
                    { new Guid("2153883d-066a-4927-bdb5-3352fa75a0e2"), null, false, "General.Countries", "Ülke Yönetimi", 0, null, null },
                    { new Guid("221a7f49-ac18-4729-9799-885f7be3d114"), null, false, "General.Cities", "Şehir/İlçe Yönetimi", 0, null, null },
                    { new Guid("23c4656b-b3c3-4753-84fd-53ba895a26e8"), null, false, "DynamicForms.Attributes", "Dinamik Form Alan Yönetimi", 0, null, null },
                    { new Guid("277699ef-e8b0-409c-ba88-3a23ee837770"), null, false, "Customers.Classifications", "Müşteri Sınıflandırma Yönetimi", 0, null, null },
                    { new Guid("28f5febe-e619-4292-b909-5055c273eb8c"), null, false, "Users.Roles", "Rol Yönetimi", 0, null, null },
                    { new Guid("2d3755db-5f7f-4e79-aa71-129dbda6245f"), null, false, "Customers.Sources", "Müşteri Kaynağı Yönetimi", 0, null, null },
                    { new Guid("3448831d-e56d-49ba-99a8-136ee4bbba66"), null, false, "Users.Departments", "Departman Yönetimi", 0, null, null },
                    { new Guid("38934ec5-cb00-4cb7-ac72-c64631e96586"), null, false, "General.Files", "Dosya Yönetimi", 0, null, null },
                    { new Guid("3acd3e0d-b950-4312-a1fe-d71259950829"), null, false, "Conversations.Chats", "Sohbet Yönetimi", 0, null, null },
                    { new Guid("3b0d9186-3c64-4b76-afef-f2ff9804738c"), null, false, "Tasks.Comments", "Görev Yorum Yönetimi", 0, null, null },
                    { new Guid("3c600b41-8152-496d-9a96-9102af74e3cd"), null, false, "Tasks.Reports", "Görev Raporlama", 0, null, null },
                    { new Guid("3c858a87-df1b-4bd0-82e5-48905739731f"), null, false, "Tasks.Management", "Görev Yönetimi", 0, null, null },
                    { new Guid("429a1097-2016-40f5-abec-1d879d86ca56"), null, false, "Customers.Sectors", "Müşteri Sektör Yönetimi", 0, null, null },
                    { new Guid("42b5022c-ed69-452c-b035-9e4c7925e156"), null, false, "Conversations.Calls", "Çağrı Yönetimi", 0, null, null },
                    { new Guid("5ca572dc-dd8b-4e51-9ef8-11faa4049a81"), null, false, "Users.Pauses", "Mola Yönetimi", 0, null, null },
                    { new Guid("64ca878d-b9b6-4e2d-b176-3686778dd12c"), null, false, "Customers.Addresses", "Müşteri Adres Yönetimi", 0, null, null },
                    { new Guid("65ba4543-69d5-404b-9deb-f8c72953fcde"), null, false, "Requests.Tickets", "Ticket Yönetimi", 0, null, null },
                    { new Guid("6f297895-c78a-486c-947c-e2e1de1d7e3f"), null, false, "General.Language", "Dil Yönetimi", 0, null, null },
                    { new Guid("7fc74060-b381-4390-8db5-5923d7b605b1"), null, false, "Users.Management", "Kullanıcı Yönetimi", 0, null, null },
                    { new Guid("87f0e782-9020-44c5-bcd8-1bc125e69102"), null, false, "Customers.Professions", "Müşteri Mesleği Yönetimi", 0, null, null },
                    { new Guid("8b3c21c1-badc-4243-a1cb-10a39208d85a"), null, false, "Conversations.CallNotes", "Çağrı Notları Yönetimi", 0, null, null },
                    { new Guid("96f554d7-bd72-4868-92f0-f6e8ec9953d6"), null, false, "DynamicForms.Records", "Dinamik Form Veri Yönetimi", 0, null, null },
                    { new Guid("a8d5c7e4-1e97-4a6b-b3ee-7cdf008ba7ab"), null, false, "Requests.TicketSubjects", "Ticket Subject Yönetimi", 0, null, null },
                    { new Guid("c1072a1a-22cc-42bf-a830-841025d6fe48"), null, false, "DynamicForms.Management", "Dinamik Form Yönetimi", 0, null, null },
                    { new Guid("cbb4ca38-8551-4559-812d-97d45a477195"), null, false, "Conversations.Autodialers", "Otomatik Çağrı Yönetimi", 0, null, null },
                    { new Guid("dd51494b-e597-4b7e-9ed0-945ec7cc601b"), null, false, "Customers.Management", "Müşteri Yönetimi", 0, null, null },
                    { new Guid("eaa2aa2b-1ae4-44b9-9a1c-c900cb931911"), null, false, "Requests.TicketComments", "Ticket Yorum Yönetimi", 0, null, null },
                    { new Guid("f7376551-0714-4f86-a40b-7cb46c67529b"), null, false, "Users.Permissions", "İzin Yönetimi", 0, null, null }
                });

            migrationBuilder.DropColumn(
                name: "Description",
                schema: "Users",
                table: "Permission");
        }
    }
}
