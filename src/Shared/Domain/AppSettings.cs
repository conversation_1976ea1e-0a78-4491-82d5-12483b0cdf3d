namespace Shared.Domain;

public class AppSettings
{
    public Guid Id { get; set; }
    public string? Title { get; set; }
    public int MailPort { get; set; }
    public string? MailServer { get; set; }
    public string? MailUser { get; set; }
    public string? MailPassword { get; set; }
    public bool MailSslEnabled { get; set; }
    public string? SmsProvider { get; set; }
    public string? SmsCompanyCode { get; set; }
    public string? SmsUserName { get; set; }
    public string? SmsPassword { get; set; }
    public string? SmsPassword2 { get; set; }
    public string? SmsUserBaslik { get; set; }
    public string? SmsFastLoginMessage { get; set; }
    public string? SmsPhoneConfirmMessage { get; set; }
    public int CacheTime { get; set; }
    public bool UseThreeCX { get; set; } = true;
    public string? ThreeCXApiUrl { get; set; }
    public string? ThreeCXSecretKey { get; set; }
    public string? ThreeCXApiUsername { get; set; }
    public string? ThreeCXApiPassword { get; set; }
    public string? WhatsAppWebhookSecret { get; set; }
    public string? DefaultPhonePrefix { get; set; } = "90";
    public string? DefaultRegion { get; set; } = "TR";
    public string WhatsAppApiUrl { get; set; } = "https://graph.facebook.com/v22.0/";
    public string? WhatsAppApiToken { get; set; }
    public string DefaultLanguage { get; set; } = "TR";
    public string MediaPath { get; set; } = "Content/Uploads";
    public string TicketCodeFormat { get; set; } = "TK{R:6}";
    public string TaskCodeFormat { get; set; } = "TSK{R:6}";
    public string DefaultBaseChatId { get; set; }
    public string ThreeCXRecordingPath { get; set; } = "C:/ProgramData/3CX/Instance1/Data/Recordings";
    public string MobilePushAccountFilePath { get; set; } = "./Content/firebase-adminsdk.json";
}
